"""
EduGuideBot v3 Hybrid Recommender
Combines MCDA and ML scores for university recommendations
"""

import logging
from typing import Dict, List, Any
import asyncio

from .mcda.scorer import calculate_mcda_score
from .ml.model import ml_recommender
try:
    from .data_loader import load_raw
    USE_DATA_LOADER = True
except ImportError:
    from .data.loader import load_university_data
    USE_DATA_LOADER = False

logger = logging.getLogger(__name__)

# Hybrid scoring weights
HYBRID_WEIGHTS = {
    'mcda': 0.7,
    'ml': 0.3
}


async def get_recommendations(user_answers: Dict[int, Dict], top_k: int = 5) -> List[Dict[str, Any]]:
    """
    Get hybrid recommendations for user based on assessment answers.
    
    Args:
        user_answers: Dictionary of user assessment answers
        top_k: Number of top recommendations to return
        
    Returns:
        List of recommended majors with scores
    """
    try:
        # Load university data using the working data loader
        if USE_DATA_LOADER:
            university_data = load_raw()
        else:
            university_data = await load_university_data()

        if not university_data:
            logger.error("No university data available")
            return []
        
        # Get user location preference for filtering
        user_location = get_user_location_preference(user_answers)
        
        # Filter majors by location if specified
        filtered_majors = filter_majors_by_location(university_data, user_location)

        if not filtered_majors:
            logger.warning("No majors found for user location preference")
            filtered_majors = university_data  # Fall back to all majors

        # Filter majors by budget if specified
        budget_filtered_majors = filter_majors_by_budget(filtered_majors, user_answers)

        if not budget_filtered_majors:
            logger.warning("No majors found within user budget, using all location-filtered majors")
            budget_filtered_majors = filtered_majors  # Fall back to location-filtered majors

        final_majors = budget_filtered_majors
        
        # Calculate scores for each major
        scored_majors = []
        
        for major in final_majors:
            try:
                # Calculate MCDA score
                mcda_score = calculate_mcda_score(user_answers, major)
                
                # Calculate ML score
                ml_score = ml_recommender.predict_score(user_answers, major)
                
                # Calculate hybrid score
                hybrid_score = (
                    mcda_score * HYBRID_WEIGHTS['mcda'] + 
                    ml_score * HYBRID_WEIGHTS['ml']
                )
                
                # Add to results with proper field mapping
                scored_majors.append({
                    'major_id': major.get('major_id', major.get('id', '')),
                    'major_name': major.get('major_name_kh', major.get('major_name_en', 'មិនមានឈ្មោះ')),
                    'major_name_kh': major.get('major_name_kh', 'មិនមានឈ្មោះ'),
                    'major_name_en': major.get('major_name_en', 'No data'),
                    'university_id': major.get('university_id', ''),
                    'university_name': major.get('university_name_kh', major.get('university_name_en', 'មិនមានឈ្មោះ')),
                    'university_name_kh': major.get('university_name_kh', 'មិនមានឈ្មោះ'),
                    'university_name_en': major.get('university_name_en', 'No data'),
                    'location': major.get('city', 'មិនមានទីតាំង'),
                    'city': major.get('city', 'មិនមានទីតាំង'),
                    'fees_usd': major.get('tuition_fees_usd', 'N/A'),
                    'tuition_fees_usd': major.get('tuition_fees_usd', 'N/A'),
                    'employment_rate': major.get('employment_rate', 'N/A'),
                    'duration': major.get('duration_years', 'N/A'),
                    'mcda_score': mcda_score,
                    'ml_score': ml_score,
                    'score': hybrid_score,
                    'hybrid_score': hybrid_score,
                    'raw_data': major
                })
                
            except Exception as e:
                logger.error(f"Error scoring major {major.get('id', 'unknown')}: {e}")
                continue
        
        # Sort by hybrid score (descending)
        scored_majors.sort(key=lambda x: x['score'], reverse=True)
        
        # Return top K recommendations
        recommendations = scored_majors[:top_k]
        
        logger.info(f"Generated {len(recommendations)} recommendations for user")
        return recommendations
        
    except Exception as e:
        logger.error(f"Error generating recommendations: {e}")
        return []


def get_user_location_preference(user_answers: Dict[int, Dict]) -> str:
    """Extract user location preference from answers."""
    try:
        # Question 3: Location preference (index 2)
        if 2 not in user_answers:
            return 'any'
        
        location_index = user_answers[2]['answer_index']
        
        location_map = {
            0: 'phnom_penh',    # ភ្នំពេញ
            1: 'siem_reap',     # សៀមរាប
            2: 'battambang',    # បាត់ដំបង
            3: 'any'            # ខេត្តផ្សេងទៀត
        }
        
        return location_map.get(location_index, 'any')
        
    except Exception as e:
        logger.error(f"Error extracting location preference: {e}")
        return 'any'


def filter_majors_by_location(university_data: List[Dict[str, Any]], user_location: str) -> List[Dict[str, Any]]:
    """Filter majors by user location preference with Khmer city name support."""
    try:
        if user_location == 'any':
            return university_data

        filtered_majors = []

        # Map user location to both English and Khmer city names
        location_map = {
            'phnom_penh': ['phnom penh', 'ភ្នំពេញ', 'pp'],
            'siem_reap': ['siem reap', 'សៀមរាប', 'sr'],
            'battambang': ['battambang', 'បាត់ដំបង', 'btb']
        }

        target_cities = location_map.get(user_location, [])

        for major in university_data:
            major_city = major.get('city', '').lower().strip()

            # Check if major city matches any of the target city variations
            for target_city in target_cities:
                if target_city.lower() in major_city or major_city in target_city.lower():
                    filtered_majors.append(major)
                    break

        logger.info(f"Filtered {len(filtered_majors)} majors for location: {user_location}")
        logger.debug(f"Target cities: {target_cities}")

        # If no majors found for specific location, log sample cities for debugging
        if len(filtered_majors) == 0:
            sample_cities = [major.get('city', 'N/A') for major in university_data[:5]]
            logger.warning(f"No majors found for {user_location}. Sample cities in data: {sample_cities}")

        return filtered_majors

    except Exception as e:
        logger.error(f"Error filtering majors by location: {e}")
        return university_data


def filter_majors_by_budget(university_data: List[Dict[str, Any]], user_answers: Dict[int, Dict]) -> List[Dict[str, Any]]:
    """Filter majors by user budget preference with strict budget limits."""
    try:
        # Question 4: Budget preference (index 3)
        if 3 not in user_answers:
            return university_data  # No budget preference specified

        user_budget_index = user_answers[3]['answer_index']

        # Budget mapping (USD per year) - same as MCDA scorer
        budget_map = {
            0: (0, 500),        # តិចជាង $500
            1: (500, 1000),     # $500–$1000
            2: (1000, 2000),    # $1000–$2000
            3: (2000, 10000)    # ច្រើនជាង $2000
        }

        budget_range = budget_map.get(user_budget_index, (0, 10000))
        max_budget = budget_range[1]

        filtered_majors = []

        for major in university_data:
            major_fees = major.get('tuition_fees_usd', 0)

            # Handle string fees
            if isinstance(major_fees, str):
                try:
                    major_fees = float(major_fees.replace('$', '').replace(',', '').replace(' ', ''))
                except (ValueError, AttributeError):
                    major_fees = 0  # If can't parse, assume free/low cost
            elif not major_fees:
                major_fees = 0  # If no fees specified, assume free/low cost

            # Include programs within budget + 20% tolerance
            tolerance_budget = max_budget * 1.2

            if major_fees <= tolerance_budget:
                filtered_majors.append(major)

        logger.info(f"Budget filtered {len(filtered_majors)} majors within ${max_budget} (+20% tolerance)")

        return filtered_majors

    except Exception as e:
        logger.error(f"Error filtering majors by budget: {e}")
        return university_data


async def get_major_details(major_id: str) -> Dict[str, Any]:
    """Get detailed information about a specific major."""
    try:
        if USE_DATA_LOADER:
            university_data = load_raw()
        else:
            university_data = await load_university_data()

        for major in university_data:
            if major.get('major_id', major.get('id', '')) == major_id:
                return {
                    'major_id': major.get('major_id', major.get('id', '')),
                    'major_name': major.get('major_name_kh', major.get('major_name_en', 'មិនមានឈ្មោះ')),
                    'university_id': major.get('university_id', ''),
                    'university_name': major.get('university_name_kh', major.get('university_name_en', 'មិនមានឈ្មោះ')),
                    'location': major.get('city', 'មិនមានទីតាំង'),
                    'fees_usd': major.get('tuition_fees_usd', 'N/A'),
                    'duration': major.get('duration_years', 'N/A'),
                    'employment_rate': major.get('employment_rate', 'N/A'),
                    'description': major.get('description_kh', major.get('description_en', 'មិនមានការពិពណ៌នា')),
                    'career_prospects': major.get('career_prospects_kh', major.get('career_prospects_en', 'មិនមានព័ត៌មាន')),
                    'internship_availability': major.get('internship_availability', 'មិនមានព័ត៌មាន'),
                    'contact_info': major.get('contact_info', 'មិនមានព័ត៌មាន'),
                    'requirements': major.get('requirements', []),
                    'subjects': major.get('subjects', [])
                }
        
        logger.warning(f"Major not found: {major_id}")
        return None
        
    except Exception as e:
        logger.error(f"Error getting major details: {e}")
        return None


async def get_university_details(university_id: str) -> Dict[str, Any]:
    """Get detailed information about a specific university."""
    try:
        if USE_DATA_LOADER:
            university_data = load_raw()
        else:
            university_data = await load_university_data()
        
        # Group majors by university
        universities = {}
        for major in university_data:
            uni_id = major.get('university_id', '')
            if uni_id not in universities:
                universities[uni_id] = {
                    'id': uni_id,
                    'name': major.get('university_name_kh', major.get('university_name_en', 'មិនមានឈ្មោះ')),
                    'city': major.get('city', 'មិនមានទីតាំង'),
                    'address': major.get('university_address', 'មិនមានអាសយដ្ឋាន'),
                    'phone': major.get('university_phone', ''),
                    'email': major.get('university_email', ''),
                    'website': major.get('university_website', ''),
                    'facebook': major.get('university_facebook', ''),
                    'campus_info': major.get('campus_info', ''),
                    'majors': []
                }
            
            universities[uni_id]['majors'].append({
                'id': major.get('major_id', major.get('id', '')),
                'name': major.get('major_name_kh', major.get('major_name_en', 'មិនមានឈ្មោះ')),
                'fees_usd': major.get('tuition_fees_usd', 'N/A')
            })
        
        if university_id in universities:
            return universities[university_id]
        
        logger.warning(f"University not found: {university_id}")
        return None
        
    except Exception as e:
        logger.error(f"Error getting university details: {e}")
        return None


def calculate_recommendation_confidence(mcda_score: float, ml_score: float) -> str:
    """Calculate confidence level for recommendation."""
    try:
        hybrid_score = mcda_score * HYBRID_WEIGHTS['mcda'] + ml_score * HYBRID_WEIGHTS['ml']
        
        if hybrid_score >= 0.8:
            return "ខ្ពស់"  # High
        elif hybrid_score >= 0.6:
            return "មធ្យម"  # Medium
        else:
            return "ទាប"   # Low
            
    except Exception as e:
        logger.error(f"Error calculating confidence: {e}")
        return "មិនដឹង"  # Unknown
