"""
EduGuideBot v3 Telegram Safe Operations
Provides safe wrappers for Telegram operations with error handling
"""

import logging
import json
from pathlib import Path
from telegram import Update, CallbackQuery
from telegram.ext import ContextTypes
from telegram.error import BadRequest, TimedOut, NetworkError

logger = logging.getLogger(__name__)

# Create logs directory if it doesn't exist
logs_dir = Path(__file__).parents[2] / "tests" / "logs"
logs_dir.mkdir(parents=True, exist_ok=True)
failure_log_path = logs_dir / "failures.log"


def log_telegram_errors(func):
    """Decorator to log Telegram errors."""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Telegram error in {func.__name__}: {e}")
            
            # Log to failure log
            error_data = {
                "function": func.__name__,
                "error": str(e),
                "error_type": type(e).__name__
            }
            
            with open(failure_log_path, "a", encoding="utf-8") as f:
                f.write(json.dumps(error_data, ensure_ascii=False) + "\n")
            
            # Return safe fallback message
            return "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
    
    return wrapper


async def safe_answer_callback(query: CallbackQuery, text: str = None) -> bool:
    """Safely answer callback query with Khmer fallback and comprehensive error handling."""
    try:
        if not query:
            logger.warning("Attempted to answer null callback query")
            return False

        if text:
            await query.answer(text)
        else:
            await query.answer("✅")  # Default success message
        return True
    except Exception as e:
        logger.error(f"Error answering callback: {e}")
        try:
            # Try fallback message
            await query.answer("❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។")
        except Exception as e2:
            logger.error(f"Fallback callback answer also failed: {e2}")
            pass
        return False


async def safe_edit_message(query: CallbackQuery, text: str, reply_markup=None, parse_mode=None) -> bool:
    """Safely edit message with Khmer fallback and comprehensive error handling."""
    try:
        if not query or not query.message:
            logger.warning("Attempted to edit message with null query or message")
            return False

        # Check if message actually needs editing to avoid unnecessary API calls
        current_text = query.message.text or ""
        current_markup = query.message.reply_markup

        if current_text == text and current_markup == reply_markup:
            logger.debug("Message content unchanged, skipping edit")
            return True

        await query.edit_message_text(
            text=text,
            reply_markup=reply_markup,
            parse_mode=parse_mode
        )
        return True
    except BadRequest as e:
        error_msg = str(e).lower()
        if "message is not modified" in error_msg:
            logger.debug("Message not modified, skipping edit")
            return True
        elif "message to edit not found" in error_msg:
            logger.warning("Message to edit not found")
            return False
        else:
            logger.error(f"BadRequest editing message: {e}")
            return False
    except Exception as e:
        logger.error(f"Error editing message: {e}")
        try:
            # Try fallback message
            await query.edit_message_text(
                "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
            )
        except Exception as e2:
            logger.error(f"Fallback message edit also failed: {e2}")
            pass
        return False


async def safe_send_message(update, context, text, reply_markup=None, parse_mode=None):
    """Safely send a message using either callback or regular message with comprehensive error handling"""
    try:
        if not update:
            logger.warning("Attempted to send message with null update")
            return False

        if update.callback_query:
            query = update.callback_query
            await safe_answer_callback(query)

            # Use safe_edit_message for consistency
            return await safe_edit_message(query, text, reply_markup=reply_markup, parse_mode=parse_mode)
        else:
            if not update.message:
                logger.warning("Attempted to reply to null message")
                return False

            await update.message.reply_text(text, reply_markup=reply_markup, parse_mode=parse_mode)
            return True

    except Exception as e:
        logger.error(f"Message send failed: {e}", exc_info=True)
        fallback_text = "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"

        try:
            if update.callback_query and update.callback_query.message:
                query = update.callback_query
                await safe_edit_message(query, fallback_text)
            elif update.message:
                await update.message.reply_text(fallback_text)
        except Exception as e2:
            logger.error(f"Fallback message send also failed: {e2}")
            pass

        return False


async def safe_reply_text(update: Update, text: str, reply_markup=None, parse_mode=None) -> bool:
    """Safely reply to a message with fallback handling."""
    try:
        if update.callback_query:
            await update.callback_query.message.reply_text(
                text=text,
                reply_markup=reply_markup,
                parse_mode=parse_mode
            )
        else:
            await update.message.reply_text(
                text=text,
                reply_markup=reply_markup,
                parse_mode=parse_mode
            )
        return True
    except Exception as e:
        logger.error(f"Error replying to message: {e}")
        try:
            fallback_text = "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
            if update.callback_query:
                await update.callback_query.message.reply_text(fallback_text)
            else:
                await update.message.reply_text(fallback_text)
        except:
            pass
        return False
