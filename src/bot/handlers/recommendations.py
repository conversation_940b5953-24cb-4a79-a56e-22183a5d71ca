"""
EduGuideBot v3 Recommendation Handlers
Handles recommendation actions and major details
"""

import logging
from typing import Dict, Any, Optional
from telegram import Update, CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes, CallbackQueryHandler, ConversationHandler

from src.bot.telegram_safe_v3 import safe_answer_callback, safe_edit_message, safe_send_message, log_telegram_errors
from src.bot.ui import create_enhanced_recommendations_view

logger = logging.getLogger(__name__)

# Define conversation state
ASSESSMENT_STATE = 1

@log_telegram_errors
async def show_recommendations(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Display top recommendations after assessment completion"""
    try:
        # Get user answers
        assessment = context.user_data.get('assessment', {})
        if not assessment:
            logger.warning("No assessment data found when showing recommendations")
            # Try to get from context if it was cleared too early
            assessment = context.user_data.get('answers', {})

        if not assessment:
            await safe_send_message(
                update,
                context,
                text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញអនុសាសន៍។ សូមព្យាយាមម្តងទៀត។"
            )
            return ConversationHandler.END

        # Get recommendations using MCDA + ML hybrid approach
        try:
            from src.core.recommender import get_recommendations
            recommendations = await get_recommendations(assessment)
            logger.info(f"Generated {len(recommendations)} recommendations using hybrid approach")
        except Exception as e:
            logger.error(f"Hybrid recommender failed: {e}")
            # Fallback to MCDA-only if ML fails
            try:
                from src.core.mcda.scorer import mcda_scorer
                recommendations = mcda_scorer(assessment)
                logger.info(f"Generated {len(recommendations)} recommendations using MCDA fallback")
            except Exception as e2:
                logger.error(f"MCDA fallback also failed: {e2}")
                recommendations = []

        if not recommendations:
            await safe_send_message(
                update,
                context,
                text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញអនុសាសន៍។ សូមព្យាយាមម្តងទៀត។"
            )
            return ConversationHandler.END

        # Cache recommendations for detail view
        context.user_data['recommendations'] = recommendations

        # Create the enhanced view
        message_text, reply_markup = create_enhanced_recommendations_view(recommendations, assessment.get('lang', 'kh'))

        # Use appropriate method to send the message
        if update.callback_query:
            await safe_edit_message(
                update.callback_query,
                text=message_text,
                reply_markup=reply_markup,
                parse_mode='HTML'
            )
        else:
            await safe_send_message(
                update,
                context,
                text=message_text,
                reply_markup=reply_markup,
                parse_mode='HTML'
            )

        # Clear assessment data but keep recommendations
        context.user_data.pop('current_question', None)
        context.user_data.pop('answers', None)

        return ASSESSMENT_STATE

    except Exception as e:
        logger.error(f"Critical error in show_recommendations: {e}", exc_info=True)
        await safe_send_message(
            update,
            context,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញអនុសាសន៍។ សូមព្យាយាមម្តងទៀត។"
        )
        return ConversationHandler.END


@log_telegram_errors
async def show_other_majors(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Show other majors at the same university"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        if not isinstance(query.data, str) or not query.data.startswith('other_majors_'):
            logger.error(f"Invalid callback data in show_other_majors: {query.data}")
            return ASSESSMENT_STATE

        # Parse callback data - handle "other_majors_id" pattern
        callback_parts = query.data.split('_')
        if len(callback_parts) >= 3:
            university_id = '_'.join(callback_parts[2:])  # Join all parts after "other_majors"
        elif len(callback_parts) == 2:
            university_id = callback_parts[1]  # Handle "other_id" pattern
        else:
            logger.error(f"Invalid callback data format: {query.data}")
            return ASSESSMENT_STATE

        # Get all programs from this university
        try:
            from src.core.data_loader import load_raw
            all_programs = load_raw()
        except ImportError:
            from src.core.data.loader import load_programs
            all_programs = load_programs()

        # Filter by university
        university_programs = [p for p in all_programs if p.get('university_id') == university_id]

        if not university_programs:
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មិនទាន់មានព័ត៌មានអំពីមុខវិជ្ជាសិក្សាផ្សេងទៀត។ សូមព្យាយាមម្តងទៀត។",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
                ]])
            )
            return ASSESSMENT_STATE

        # Create a list of majors
        if context.user_data.get('lang', 'kh') == 'kh':
            message_parts = ["📚 <b>មុខវិជ្ជាសិក្សាផ្សេងទៀត:</b>\n"]
            for program in university_programs[:10]:  # Limit to 10 for UX
                message_parts.append(
                    f"{program.get('major_name_kh', 'មិនទាន់មានទិន្នន័យ')}\n"
                )
        else:
            message_parts = ["📚 <b>Other Majors:</b>\n"]
            for program in university_programs[:10]:
                message_parts.append(
                    f"{program.get('major_name_en', 'No data')}\n"
                )

        # Add back button
        message_parts.append("\n🔙 ត្រឡប់ទៅបញ្ជី")
        reply_markup = InlineKeyboardMarkup([[
            InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
        ]])

        await safe_edit_message(
            query,
            text='\n'.join(message_parts),
            reply_markup=reply_markup,
            parse_mode='HTML'
        )

        return ASSESSMENT_STATE

    except Exception as e:
        logger.error(f"Error in show_other_majors: {e}")
        await safe_edit_message(
            query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញមុខវិជ្ជាសិក្សាផ្សេងទៀត។ សូមព្យាយាមម្តងទៀត។",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
            ]]),
            parse_mode='HTML'
        )
        return ASSESSMENT_STATE


@log_telegram_errors
async def show_university_location(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Show university location and map"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        if not isinstance(query.data, str) or not query.data.startswith('location_'):
            logger.error(f"Invalid callback data in show_university_location: {query.data}")
            return ASSESSMENT_STATE

        # Parse callback data - handle "location_id" pattern
        callback_parts = query.data.split('_')
        if len(callback_parts) >= 3:
            university_id = '_'.join(callback_parts[2:])  # Join all parts after "location"
        elif len(callback_parts) == 2:
            university_id = callback_parts[1]  # Handle "location_id" pattern
        else:
            logger.error(f"Invalid callback data format: {query.data}")
            return ASSESSMENT_STATE

        # Get university details
        try:
            from src.core.data.loader import get_university_by_id
            university = get_university_by_id(university_id)
        except ImportError:
            # Fallback to loading all data and filtering
            try:
                from src.core.data_loader import load_raw
                all_programs = load_raw()
                university_programs = [p for p in all_programs if p.get('university_id') == university_id]
                university = university_programs[0] if university_programs else None
            except Exception as e:
                logger.error(f"Failed to load university data: {e}")
                university = None

        if not university:
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញទីតាំងសាកលវិទ្យាល័យ។ សូមព្យាយាមម្តងទៀត។",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
                ]]),
                parse_mode='HTML'
            )
            return ASSESSMENT_STATE



        # Create location message
        location = university.get('university', {}).get('location', {}) if isinstance(university, dict) and 'university' in university else university.get('location', {})
        contact = university.get('university', {}).get('contact', {}) if isinstance(university, dict) and 'university' in university else university.get('contact', {})

        if context.user_data.get('lang', 'kh') == 'kh':
            message_text = [
                "📍 <b>ព័ត៌មានទីតាំងសាកលវិទ្យាល័យ</b>",
                f"ទីតាំងប្រចាំទី: {location.get('address_kh', 'មិនទាន់មានទិន្នន័យ')}",
                f"ទីក្រុង: {location.get('city', 'មិនទាន់មានទិន្នន័យ')}",
                f"ប្រទេស: {location.get('country', 'មិនទាន់មានទិន្នន័យ')}",
                f"ផែនទី: {location.get('latitude', 'មិនទាន់មានទិន្នន័យ')} , {location.get('longitude', 'មិនទាន់មានទិន្នន័យ')}"
            ]

            # Add social media if available
            if contact.get('social_media', {}):
                message_text.extend([
                    "",
                    "<b>📱 បណ្តាញសង្គម:</b>"
                ])
                if contact['social_media'].get('facebook'):
                    message_text.append(f"🔹 Facebook: {contact['social_media']['facebook']}")
                if contact['social_media'].get('youtube'):
                    message_text.append(f"🔸 YouTube: {contact['social_media']['youtube']}")
                if contact['social_media'].get('telegram'):
                    message_text.append(f"🔸 Telegram: {contact['social_media']['telegram']}")
                if contact.get('website'):
                    message_text.append(f"🌐 គេហទំព័រ: {contact['website']}")

            message_text.append("\n🔙 ត្រឡប់ទៅបញ្ជី")

        else:
            message_text = [
                "<b>📍 University Location Details</b>",
                f"Address: {location.get('address_en', 'N/A')}",
                f"City: {location.get('city', 'N/A')}",
                f"Country: {location.get('country', 'N/A')}",
                f"Map Coordinates: {location.get('latitude', 'N/A')} , {location.get('longitude', 'N/A')}"
            ]

            # Add social media info
            if contact.get('social_media', {}):
                message_text.extend([
                    "",
                    "<b>📱 Social Media:</b>"
                ])
                if contact['social_media'].get('facebook'):
                    message_text.append(f"🔹 Facebook: {contact['social_media']['facebook']}")
                if contact['social_media'].get('youtube'):
                    message_text.append(f"🔸 YouTube: {contact['social_media']['youtube']}")
                if contact['social_media'].get('telegram'):
                    message_text.append(f"🔸 Telegram: {contact['social_media']['telegram']}")

            # Add website
            if contact.get('website'):
                message_text.append(f"🌐 Website: {contact['website']}")

            message_text.append("\n🔙 Back to list")

        reply_markup = InlineKeyboardMarkup([[
            InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
        ]])

        await safe_edit_message(
            query,
            text='\n'.join(message_text),
            reply_markup=reply_markup,
            parse_mode='HTML'
        )

        return ASSESSMENT_STATE

    except Exception as e:
        logger.error(f"Error in show_university_location: {e}")
        await safe_edit_message(
            query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញទីតាំងសាកលវិទ្យាល័យ។ សូមព្យាយាមម្តងទៀត។",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
            ]]),
            parse_mode='HTML'
        )
        return ASSESSMENT_STATE


@log_telegram_errors
async def show_university_contact(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Show university contact information"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        if not isinstance(query.data, str) or not query.data.startswith('contact_'):
            logger.error(f"Invalid callback data in show_university_contact: {query.data}")
            return ASSESSMENT_STATE

        # Parse callback data - handle "contact_id" pattern
        callback_parts = query.data.split('_')
        if len(callback_parts) >= 3:
            university_id = '_'.join(callback_parts[2:])  # Join all parts after "contact"
        elif len(callback_parts) == 2:
            university_id = callback_parts[1]  # Handle "contact_id" pattern
        else:
            logger.error(f"Invalid callback data format: {query.data}")
            return ASSESSMENT_STATE

        # Get university details
        try:
            from src.core.data.loader import get_university_by_id
            university = get_university_by_id(university_id)
        except ImportError:
            # Fallback to loading all data and filtering
            try:
                from src.core.data_loader import load_raw
                all_programs = load_raw()
                university_programs = [p for p in all_programs if p.get('university_id') == university_id]
                university = university_programs[0] if university_programs else None
            except Exception as e:
                logger.error(f"Failed to load university data: {e}")
                university = None

        if not university:
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញព័ត៌មានទំនាក់ទំនង។ សូមព្យាយាមម្តងទៀត។",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
                ]]),
                parse_mode='HTML'
            )
            return ASSESSMENT_STATE

        # Extract contact info
        contact_info = university.get('university', {}).get('contact', {}) if isinstance(university, dict) and 'university' in university else university.get('contact', {})

        # Build contact message
        if context.user_data.get('lang', 'kh') == 'kh':
            message_parts = ["📞 <b>ព័ត៌មានទំនាក់ទំនងសាកលវិទ្យាល័យ</b>"]

            if contact_info.get('phone'):
                phones = contact_info['phone']
                message_parts.append(f"\nថ្មីៗ: {', '.join(phones) if isinstance(phones, list) else phones}")

            if contact_info.get('email'):
                message_parts.append(f"អ៊ីម៉ែល: {contact_info['email']}")

            if contact_info.get('social_media', {}):
                message_parts.append("<b>\n\n📱 បណ្តាញសង្គម:</b>")
                if contact_info['social_media'].get('facebook'):
                    message_parts.append(f"🔹 Facebook: {contact_info['social_media']['facebook']}")
                if contact_info['social_media'].get('youtube'):
                    message_parts.append(f"🔸 YouTube: {contact_info['social_media']['youtube']}")
                if contact_info['social_media'].get('telegram'):
                    message_parts.append(f"🔸 Telegram: {contact_info['social_media']['telegram']}")

            if contact_info.get('website'):
                message_parts.append(f"\n🌐 គេហទំព័រ: {contact_info['website']}")

            message_parts.append("\n\n🔙 ត្រឡប់ទៅបញ្ជី")

        else:
            message_parts = ["📞 <b>University Contact Information</b>"]

            if contact_info.get('phone'):
                phones = contact_info['phone']
                message_parts.append(f"\nPhone: {', '.join(phones) if isinstance(phones, list) else phones}")

            if contact_info.get('email'):
                message_parts.append(f"Email: {contact_info['email']}")

            message_parts.append("<b>\n\n📱 Social Media:</b>")
            if contact_info.get('social_media', {}).get('facebook'):
                message_parts.append(f"🔹 Facebook: {contact_info['social_media']['facebook']}")
            if contact_info.get('social_media', {}).get('youtube'):
                message_parts.append(f"🔸 YouTube: {contact_info['social_media']['youtube']}")
            if contact_info.get('social_media', {}).get('telegram'):
                message_parts.append(f"🔸 Telegram: {contact_info['social_media']['telegram']}")

            message_parts.append(f"\n\n🌐 Website: {contact_info.get('website', 'N/A')}")
            message_parts.append("\n\n🔙 Back to list")

        # Add back button
        reply_markup = InlineKeyboardMarkup([[
            InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
        ]])

        await safe_edit_message(
            query,
            text='\n'.join(message_parts),
            reply_markup=reply_markup,
            parse_mode='HTML'
        )

        return ASSESSMENT_STATE

    except Exception as e:
        logger.error(f"Error in show_university_contact: {e}")
        await safe_edit_message(
            query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញព័ត៌មានទំនាក់ទំនង។ សូមព្យាយាមម្តងទៀត។",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
            ]]),
            parse_mode='HTML'
        )
        return ASSESSMENT_STATE


# Register all detail handlers
def register_detail_handlers(application):
    """Register all detail view handlers"""
    from telegram.ext import CallbackQueryHandler

    application.add_handler(CallbackQueryHandler(show_other_majors, pattern="^other_majors_"))
    application.add_handler(CallbackQueryHandler(show_university_location, pattern="^location_"))
    application.add_handler(CallbackQueryHandler(show_university_contact, pattern="^contact_"))

    # Import and register the back_to_recommendations handler from details.py
    try:
        from src.bot.handlers.details import back_to_recommendations
        application.add_handler(CallbackQueryHandler(back_to_recommendations, pattern="^back_to_recommendations$"))
    except ImportError:
        logger.warning("Could not import back_to_recommendations from details.py")


