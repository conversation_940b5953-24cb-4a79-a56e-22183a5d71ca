"""
EduGuideBot v3 Details Handler
Handles detailed view of major recommendations and navigation
"""

import logging
from typing import Dict, Any, List, Optional
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from telegram.ext import ContextTypes, CallbackQueryHandler

from src.bot.telegram_safe_v3 import safe_edit_message, safe_answer_callback, log_telegram_errors
from src.core.data.loader import get_university_by_id

logger = logging.getLogger(__name__)

# Assessment state constant
ASSESSMENT_STATE = 1


@log_telegram_errors
async def show_major_details(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Show detailed information about a recommended major"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Extract major ID from callback data
        if not isinstance(query.data, str) or not query.data.startswith('details_'):
            logger.error(f"Invalid callback data in show_major_details: {query.data}")
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មិនអាចរកមុខវិជ្ជាសិក្សាបាន។ សូមព្យាយាមម្តងទៀត។"
            )
            return ASSESSMENT_STATE

        _, major_id = query.data.split('_', 1)

        # Get cached recommendations
        recommendations = context.user_data.get('recommendations', [])

        # Find selected program
        selected_rec = None
        for rec in recommendations:
            if rec.get('major_id') == major_id:
                selected_rec = rec
                break

        if not selected_rec:
            # Try to find in all programs
            from src.core.data.loader import load_programs
            all_programs = load_programs()
            for prog in all_programs:
                if prog.get('major_id') == major_id:
                    selected_rec = prog
                    break

        if not selected_rec:
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មិនអាចរកមុខវិជ្ជាសិក្សាបាន។ សូមព្យាយាមម្តងទៀត។"
            )
            return ASSESSMENT_STATE
            
        # Build message content
        if context.user_data.get('lang', 'kh') == 'kh':
            message_parts = [
                f"<b>📌 ព័ត៌មានលម្អិតនៃជំនាញទី {major_id}:</b>",
                f"🎓 ជំនាញ: {selected_rec.get('major_name_kh', selected_rec.get('major_name', 'មិនទាន់មានទិន្នន័យ'))}",
                f"🏫 សាកលវិទ្យាល័យ: {selected_rec.get('university_kh', selected_rec.get('university', 'មិនទាន់មានទិន្នន័យ'))}"
            ]
        else:
            message_parts = [
                f"<b>📌 Major Details {major_id}:</b>",
                f"🎓 Major: {selected_rec.get('major_name_en', selected_rec.get('major_name', 'N/A'))}",
                f"🏫 University: {selected_rec.get('university_en', selected_rec.get('university', 'N/A'))}"
            ]

        # Add location and fees
        if selected_rec.get('location'):
            message_parts.append(f"📍 ទីតាំង: {selected_rec['location']}")
        if selected_rec.get('fees_khr') and selected_rec.get('fees_usd'):
            message_parts.append(f"💰 ថ្លៃសិក្សា: ៛{selected_rec['fees_khr']} / ${selected_rec['fees_usd']}")

        # Add employment rate with safety checks
        employment_rate = selected_rec.get('employment_rate', 'មិនទាន់មានទិន្នន័យ')
        if isinstance(employment_rate, str) and employment_rate.replace('%', '').isdigit():
            rate = int(employment_rate.replace('%', ''))
            stars = '★' * round(rate/20) + '☆' * (5 - round(rate/20))
        else:
            stars = '★★★☆☆'

        if context.user_data.get('lang', 'kh') == 'kh':
            message_parts.append(f"📈 អត្រារកការងារ: {stars}")
        else:
            message_parts.append(f"📈 Employment Rate: {stars}")
            
        # Create keyboard with persistent back button
        keyboard = [[
            InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
        ]]

        reply_markup = InlineKeyboardMarkup(keyboard)

        # Send the message
        await safe_edit_message(
            query=query,
            text='\n'.join(message_parts),
            reply_markup=reply_markup,
            parse_mode='HTML'
        )

        return ASSESSMENT_STATE
            
    except Exception as e:
        logger.error(f"Error in show_major_details: {e}", exc_info=True)
        await safe_edit_message(
            query=query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញព័ត៌មានលម្អិត។ សូមព្យាយាមម្តងទៀត។",
            reply_markup=None
        )
        return None


@log_telegram_errors
async def back_to_recommendations(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Return to the main recommendation list"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)
        
        # If we have cached recommendations, use them
        recommendations = context.user_data.get('recommendations')
        if not recommendations:
            # Try to regenerate recommendations based on existing assessment
            assessment = context.user_data.get('assessment', {})
            if assessment:
                from src.core.recommender import get_recommendations
                recommendations = await get_recommendations(assessment)
                if recommendations:
                    context.user_data['recommendations'] = recommendations
                else:
                    await safe_edit_message(
                        query,
                        text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញអនុសាសន៍។ សូមព្យាយាមម្តងទៀត។",
                        reply_markup=None
                    )
                    return None
                    
        if recommendations:
            # Show recommendations with back button
            from src.bot.handlers.recommendations import show_recommendations
            return await show_recommendations(update, context)
            
        # If no recommendations at all, go back to start
        from src.bot.handlers.assessment import start_assessment
        return await start_assessment(update, context)
        
    except Exception as e:
        logger.error(f"Error in back_to_recommendations: {e}", exc_info=True)
        await safe_edit_message(
            query=query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការត្រឡប់ទៅបញ្ជី។ សូមព្យាយាមម្តងទៀត។",
            reply_markup=None
        )
        return None


def register_handlers(application):
    """Register handlers for detail view and navigation"""
    from telegram.ext import CallbackQueryHandler

    application.add_handler(CallbackQueryHandler(show_major_details, pattern="^details_"))
    application.add_handler(CallbackQueryHandler(back_to_recommendations, pattern="^back_to_recommendations$"))
