"""
EduGuideBot v3 Details Handler
Handles detailed view of major recommendations and navigation
"""

import logging
from typing import Dict, Any, List, Optional
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from telegram.ext import ContextTypes, CallbackQueryHandler

from src.bot.telegram_safe_v3 import safe_edit_message, safe_answer_callback, log_telegram_errors
from src.core.data.loader import get_university_by_id

logger = logging.getLogger(__name__)

# Assessment state constant
ASSESSMENT_STATE = 1


@log_telegram_errors
async def show_major_details(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Show detailed information about a recommended major"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Extract major ID from callback data
        if not isinstance(query.data, str) or not query.data.startswith('details_'):
            logger.error(f"Invalid callback data in show_major_details: {query.data}")
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មិនអាចរកមុខវិជ្ជាសិក្សាបាន។ សូមព្យាយាមម្តងទៀត។"
            )
            return ASSESSMENT_STATE

        _, major_id = query.data.split('_', 1)

        # Get cached recommendations
        recommendations = context.user_data.get('recommendations', [])

        # Find selected program in cached recommendations
        selected_rec = None
        for rec in recommendations:
            if rec.get('major_id') == major_id:
                selected_rec = rec
                break

        if not selected_rec:
            # Try to find in all programs using the working data loader
            try:
                from src.core.data_loader import load_raw
                all_programs = load_raw()
                for prog in all_programs:
                    if prog.get('major_id') == major_id:
                        selected_rec = prog
                        break
            except Exception as e:
                logger.error(f"Error loading programs for details: {e}")

        # Log for debugging
        logger.info(f"Looking for major_id: {major_id}")
        logger.info(f"Found in recommendations: {selected_rec is not None}")
        if not selected_rec and recommendations:
            sample_ids = [rec.get('major_id', 'N/A') for rec in recommendations[:3]]
            logger.info(f"Sample major_ids in recommendations: {sample_ids}")

        if not selected_rec:
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មិនអាចរកមុខវិជ្ជាសិក្សាបាន។ សូមព្យាយាមម្តងទៀត។"
            )
            return ASSESSMENT_STATE
            
        # Build comprehensive message content
        if context.user_data.get('lang', 'kh') == 'kh':
            message_parts = [
                f"<b>📌 ព័ត៌មានលម្អិតនៃជំនាញ:</b>",
                f"🎓 <b>ជំនាញ:</b> {selected_rec.get('major_name_kh', selected_rec.get('major_name', 'មិនទាន់មានទិន្នន័យ'))}",
                f"🏫 <b>សាកលវិទ្យាល័យ:</b> {selected_rec.get('university_name_kh', selected_rec.get('university_name', 'មិនទាន់មានទិន្នន័យ'))}"
            ]
        else:
            message_parts = [
                f"<b>📌 Major Details:</b>",
                f"🎓 <b>Major:</b> {selected_rec.get('major_name_en', selected_rec.get('major_name', 'N/A'))}",
                f"🏫 <b>University:</b> {selected_rec.get('university_name_en', selected_rec.get('university_name', 'N/A'))}"
            ]

        # Add location
        location = selected_rec.get('city', selected_rec.get('location', 'មិនទាន់មានទីតាំង'))
        message_parts.append(f"📍 <b>ទីតាំង:</b> {location}")

        # Add comprehensive fees information
        fees_usd = selected_rec.get('tuition_fees_usd', selected_rec.get('fees_usd', 'មិនទាន់មានទិន្នន័យ'))
        fees_khr = selected_rec.get('tuition_fees_khr', selected_rec.get('fees_khr', 'មិនទាន់មានទិន្នន័យ'))

        if fees_usd != 'មិនទាន់មានទិន្នន័យ' or fees_khr != 'មិនទាន់មានទិន្នន័យ':
            fee_parts = []
            if fees_usd != 'មិនទាន់មានទិន្នន័យ':
                fee_parts.append(f"${fees_usd}")
            if fees_khr != 'មិនទាន់មានទិន្នន័យ':
                fee_parts.append(f"៛{fees_khr}")
            message_parts.append(f"💰 <b>ថ្លៃសិក្សា:</b> {' / '.join(fee_parts)}")
        else:
            message_parts.append(f"💰 <b>ថ្លៃសិក្សា:</b> មិនទាន់មានទិន្នន័យ")

        # Add duration
        duration = selected_rec.get('duration_years', selected_rec.get('duration', 'មិនទាន់មានទិន្នន័យ'))
        message_parts.append(f"📚 <b>រយៈពេលសិក្សា:</b> {duration} ឆ្នាំ")

        # Add employment rate with comprehensive handling
        employment_rate = selected_rec.get('employment_rate', 'មិនទាន់មានទិន្នន័យ')
        try:
            if isinstance(employment_rate, str) and employment_rate.replace('%', '').strip().isdigit():
                rate = int(employment_rate.replace('%', '').strip())
                stars = '★' * min(5, max(1, round(rate/20))) + '☆' * max(0, 5 - round(rate/20))
                message_parts.append(f"📈 <b>អត្រារកការងារ:</b> {stars} ({rate}%)")
            elif isinstance(employment_rate, (int, float)):
                rate = int(employment_rate)
                stars = '★' * min(5, max(1, round(rate/20))) + '☆' * max(0, 5 - round(rate/20))
                message_parts.append(f"📈 <b>អត្រារកការងារ:</b> {stars} ({rate}%)")
            else:
                message_parts.append(f"📈 <b>អត្រារកការងារ:</b> ★★★☆☆")
        except (ValueError, TypeError):
            message_parts.append(f"📈 <b>អត្រារកការងារ:</b> ★★★☆☆")

        # Add description if available
        description = selected_rec.get('description_kh', selected_rec.get('description_en', selected_rec.get('description', '')))
        if description and description != 'មិនទាន់មានទិន្នន័យ':
            # Truncate long descriptions
            if len(description) > 200:
                description = description[:200] + "..."
            message_parts.append(f"\n📝 <b>ការពិពណ៌នា:</b>\n{description}")

        # Add career prospects if available
        career_prospects = selected_rec.get('career_prospects_kh', selected_rec.get('career_prospects_en', ''))
        if career_prospects and career_prospects != 'មិនទាន់មានទិន្នន័យ':
            if len(career_prospects) > 150:
                career_prospects = career_prospects[:150] + "..."
            message_parts.append(f"\n💼 <b>ឱកាសការងារ:</b>\n{career_prospects}")

        # Add requirements if available
        requirements = selected_rec.get('requirements', [])
        if requirements and isinstance(requirements, list) and len(requirements) > 0:
            req_text = ", ".join(requirements[:3])  # Show first 3 requirements
            message_parts.append(f"\n📋 <b>តម្រូវការ:</b> {req_text}")

        # Create enhanced keyboard with multiple options
        keyboard = [
            [
                InlineKeyboardButton("📞 ទំនាក់ទំនង", callback_data=f"contact_{selected_rec.get('university_id', 'unknown')}"),
                InlineKeyboardButton("📍 ទីតាំង", callback_data=f"location_{selected_rec.get('university_id', 'unknown')}")
            ],
            [
                InlineKeyboardButton("🏫 មុខជំនាញផ្សេងទៀត", callback_data=f"other_majors_{selected_rec.get('university_id', 'unknown')}")
            ],
            [
                InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
            ]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        # Send the message
        await safe_edit_message(
            query=query,
            text='\n'.join(message_parts),
            reply_markup=reply_markup,
            parse_mode='HTML'
        )

        return ASSESSMENT_STATE
            
    except Exception as e:
        logger.error(f"Error in show_major_details: {e}", exc_info=True)
        await safe_edit_message(
            query=query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញព័ត៌មានលម្អិត។ សូមព្យាយាមម្តងទៀត។",
            reply_markup=None
        )
        return None


@log_telegram_errors
async def back_to_recommendations(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Return to the main recommendation list without loops"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Get cached recommendations
        recommendations = context.user_data.get('recommendations', [])

        if not recommendations:
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញអនុសាសន៍។ សូមចាប់ផ្ដើមការវាយតម្លៃម្តងទៀត។",
                reply_markup=None
            )
            return ASSESSMENT_STATE

        # Create recommendations display directly without calling show_recommendations
        # to avoid navigation loops
        rec_text = "🎯 ការណែនាំសម្រាប់អ្នក\n\n"
        rec_text += "ខាងក្រោមនេះជាមុខជំនាញដែលសមស្របបំផុតសម្រាប់អ្នក:\n\n"

        for i, rec in enumerate(recommendations[:5], 1):
            name_kh = rec.get('major_name_kh', rec.get('major_name_en', 'មិនមានឈ្មោះ'))
            uni_kh = rec.get('university_name_kh', rec.get('university_name_en', 'មិនមានឈ្មោះ'))
            location = rec.get('city', rec.get('location', 'មិនមានទីតាំង'))
            fees = rec.get('tuition_fees_usd', rec.get('fees_usd', 'N/A'))

            # Get hybrid score safely
            hybrid_score = rec.get('hybrid_score', rec.get('score', 0.5))
            confidence = get_confidence_stars(hybrid_score)

            rec_text += f"{i}. {name_kh}\n"
            rec_text += f"🏫 {uni_kh}\n"
            rec_text += f"📍 {location}\n"
            if fees != 'N/A':
                rec_text += f"💰 ${fees} USD\n"
            rec_text += f"⭐ {confidence} ({hybrid_score * 100:.0f}% ត្រូវគ្នា)\n\n"

        # Create action buttons for each recommendation
        keyboard_buttons = []
        for i, rec in enumerate(recommendations[:5]):
            major_id = rec.get('major_id', f'unknown_{i}')
            keyboard_buttons.append([
                InlineKeyboardButton(f"🔍 ព័ត៌មានបន្ថែម #{i+1}", callback_data=f"details_{major_id}")
            ])

        keyboard_buttons.append([
            InlineKeyboardButton("🔄 ធ្វើតេស្តម្តងទៀត", callback_data="restart_assessment")
        ])

        from telegram import InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup(keyboard_buttons)

        await safe_edit_message(query, rec_text, keyboard)

        return ASSESSMENT_STATE

    except Exception as e:
        logger.error(f"Error in back_to_recommendations: {e}", exc_info=True)
        await safe_edit_message(
            query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការត្រឡប់ទៅបញ្ជី។ សូមព្យាយាមម្តងទៀត។",
            reply_markup=None
        )
        return ASSESSMENT_STATE


def get_confidence_stars(score: float) -> str:
    """Convert score to star rating."""
    if score >= 0.8:
        return "★★★★★"
    elif score >= 0.6:
        return "★★★★☆"
    elif score >= 0.4:
        return "★★★☆☆"
    else:
        return "★★☆☆☆"


def register_handlers(application):
    """Register handlers for detail view and navigation"""
    from telegram.ext import CallbackQueryHandler

    application.add_handler(CallbackQueryHandler(show_major_details, pattern="^details_"))
    application.add_handler(CallbackQueryHandler(back_to_recommendations, pattern="^back_to_recommendations$"))
