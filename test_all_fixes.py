#!/usr/bin/env python3
"""
Test all the critical fixes: Back navigation, Budget filtering, Detail view
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

async def test_budget_filtering():
    """Test the budget filtering with $500 budget"""
    print("💰 Testing Budget Filtering...")
    
    try:
        from src.core.recommender import filter_majors_by_budget
        from src.core.data_loader import load_raw
        
        # Load all programs
        all_programs = load_raw()
        print(f"📊 Loaded {len(all_programs)} total programs")
        
        # Test $500 budget (index 0)
        user_answers = {
            3: {'answer_index': 0, 'answer_text': 'តិចជាង $500'}  # Budget question
        }
        
        filtered = filter_majors_by_budget(all_programs, user_answers)
        print(f"✅ Budget filtered: {len(filtered)} programs for $500 budget")
        
        # Check if any programs exceed $600 (500 + 20% tolerance)
        over_budget_count = 0
        for program in filtered:
            fees = program.get('tuition_fees_usd', 0)
            if isinstance(fees, str):
                try:
                    fees = float(fees.replace('$', '').replace(',', ''))
                except:
                    fees = 0
            
            if fees > 600:  # $500 + 20% tolerance
                over_budget_count += 1
                if over_budget_count <= 3:
                    print(f"   ⚠️  Found over-budget: {program.get('major_name_kh', 'Unknown')} - ${fees}")
        
        if over_budget_count == 0:
            print("   ✅ All filtered programs are within budget!")
            return True
        else:
            print(f"   ❌ Found {over_budget_count} programs over budget")
            return False
            
    except Exception as e:
        print(f"❌ Budget filtering test failed: {e}")
        return False


async def test_back_navigation():
    """Test the back navigation logic"""
    print("\n🔙 Testing Back Navigation...")
    
    try:
        from src.bot.handlers.details import back_to_recommendations
        from unittest.mock import AsyncMock, MagicMock
        
        # Create mock objects
        query = AsyncMock()
        query.data = "back_to_recommendations"
        
        update = AsyncMock()
        update.callback_query = query
        
        context = AsyncMock()
        context.user_data = {
            'recommendations': [
                {
                    'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
                    'university_name_kh': 'សាកលវិទ្យាល័យភូមិន្ទភ្នំពេញ',
                    'city': 'ភ្នំពេញ',
                    'tuition_fees_usd': 400,
                    'hybrid_score': 0.85,
                    'major_id': 'cs-001'
                }
            ]
        }
        
        # Test the function
        result = await back_to_recommendations(update, context)
        
        # Check if it returns the correct state
        if result is not None:
            print("   ✅ Back navigation function executed successfully")
            print(f"   ✅ Returned state: {result}")
            return True
        else:
            print("   ❌ Back navigation returned None")
            return False
            
    except Exception as e:
        print(f"❌ Back navigation test failed: {e}")
        return False


async def test_detail_view_enhancement():
    """Test the enhanced detail view"""
    print("\n🔍 Testing Enhanced Detail View...")
    
    try:
        from src.bot.handlers.details import show_major_details
        from unittest.mock import AsyncMock
        
        # Create mock objects
        query = AsyncMock()
        query.data = "details_cs-001"
        
        update = AsyncMock()
        update.callback_query = query
        
        context = AsyncMock()
        context.user_data = {
            'recommendations': [
                {
                    'major_id': 'cs-001',
                    'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
                    'university_name_kh': 'សាកលវិទ្យាល័យភូមិន្ទភ្នំពេញ',
                    'city': 'ភ្នំពេញ',
                    'tuition_fees_usd': 400,
                    'employment_rate': '85%',
                    'duration_years': 4,
                    'description_kh': 'មុខជំនាញវិទ្យាសាស្ត្រកុំព្យូទ័រ',
                    'career_prospects_kh': ['អ្នកអភិវឌ្ឍន៍កម្មវិធី', 'វិស្វករកម្មវិធី'],
                    'hybrid_score': 0.85
                }
            ]
        }
        
        # Test the function (it should not crash)
        try:
            await show_major_details(update, context)
            print("   ✅ Detail view function executed without errors")
            return True
        except Exception as detail_error:
            print(f"   ❌ Detail view function failed: {detail_error}")
            return False
            
    except Exception as e:
        print(f"❌ Detail view test failed: {e}")
        return False


async def test_handler_registration():
    """Test if handlers are properly registered"""
    print("\n🔧 Testing Handler Registration...")
    
    try:
        from src.bot.app import create_bot_application
        import os
        
        # Use a dummy token for testing
        token = os.getenv('TELEGRAM_BOT_TOKEN', 'dummy_token_for_testing')
        
        if token == 'dummy_token_for_testing':
            print("   ⚠️  Using dummy token for testing")
        
        app = await create_bot_application(token)
        
        # Count handlers
        total_handlers = sum(len(handlers) for handlers in app.handlers.values())
        print(f"   ✅ Total handlers registered: {total_handlers}")
        
        # Check for specific patterns
        callback_patterns = []
        for group_handlers in app.handlers.values():
            for handler in group_handlers:
                if hasattr(handler, 'pattern') and handler.pattern:
                    callback_patterns.append(handler.pattern.pattern)
        
        print(f"   ✅ Callback patterns: {len(callback_patterns)}")
        
        # Check for back button handler
        back_patterns = [p for p in callback_patterns if 'back' in p.lower() or 'BACK' in p]
        if back_patterns:
            print(f"   ✅ Back button handlers found: {back_patterns}")
            return True
        else:
            print("   ❌ No back button handlers found")
            return False
            
    except Exception as e:
        print(f"❌ Handler registration test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Testing All Critical Fixes\n")
    
    test1 = await test_budget_filtering()
    test2 = await test_back_navigation()
    test3 = await test_detail_view_enhancement()
    test4 = await test_handler_registration()
    
    print(f"\n📊 Test Results:")
    print(f"   Budget Filtering: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"   Back Navigation: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"   Detail View: {'✅ PASS' if test3 else '❌ FAIL'}")
    print(f"   Handler Registration: {'✅ PASS' if test4 else '❌ FAIL'}")
    
    overall_pass = test1 and test2 and test3 and test4
    print(f"\n🎯 Overall Status: {'✅ ALL FIXES WORKING' if overall_pass else '❌ SOME FIXES FAILED'}")
    
    if overall_pass:
        print("\n🎉 All critical fixes are working!")
        print("✅ Budget filtering respects $500 limit")
        print("✅ Back navigation works without loops")
        print("✅ Detail view shows comprehensive information")
        print("✅ Handlers are properly registered")
        print("\n🚀 Your bot is ready for the university presentation!")
    else:
        print("\n⚠️  Some fixes still need work.")
    
    return overall_pass


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
